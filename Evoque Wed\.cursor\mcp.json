{"mcpServers": {"browser-use": {"command": "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\Scripts\\uv.exe", "args": ["--directory", "C:/Users/<USER>/Downloads/Evoque Wed/mcp-browser-use", "run", "--active", "mcp-server-browser-use"], "env": {"OPENROUTER_API_KEY": "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", "MCP_MODEL_PROVIDER": "openrouter", "MCP_MODEL_NAME": "qwen/qwen3-235b-a22b:free", "BROWSER_HEADLESS": "true", "BROWSER_USE_LOGGING_LEVEL": "INFO", "MCP_USE_OWN_BROWSER": "true", "CHROME_CDP": "http://localhost:9222", "PYTHONIOENCODING": "utf-8", "PYTHONUNBUFFERED": "1", "PYTHONUTF8": "1"}}, "context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "env": {"OPENROUTER_API_KEY": "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"}}, "taskmaster-ai": {"command": "cmd", "args": ["/c", "npx", "-y", "--package=task-master-ai@latest", "task-master-ai"], "env": {"OPENROUTER_API_KEY": "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", "PERPLEXITY_API_KEY": "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", "MCP_MODEL_PROVIDER": "openrouter", "MCP_MODEL_NAME": "anthropic/claude-3.7-sonnet", "PERPLEXITY_MODEL": "sonar-pro", "MAX_TOKENS": 8192, "TEMPERATURE": 0.2, "DEFAULT_SUBTASKS": 5, "DEFAULT_PRIORITY": "medium"}}, "BrowserTools": {"type": "stdio", "command": "cmd", "args": ["/c", "npx", "-y", "@agentdeskai/browser-tools-mcp@1.2.0"], "env": {"OPENROUTER_API_KEY": "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"}}, "playwright": {"command": "cmd", "args": ["/c", "npx", "-y", "@playwright/mcp@latest", "--headless"], "env": {"OPENROUTER_API_KEY": "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", "BROWSER_HEADLESS": "true", "PYTHONIOENCODING": "utf-8", "PYTHONUNBUFFERED": "1", "PYTHONUTF8": "1"}}}}